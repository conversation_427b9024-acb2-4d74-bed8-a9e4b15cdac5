# SideView Electron - Migration Guide

## Overview

This is the new TypeScript/Electron implementation of SideView, migrated from the original C# .NET version. The migration maintains all core functionality while providing better cross-platform support and development experience.

## Migration Status

### ✅ Completed
- [x] Project structure and build system
- [x] Core module (Event Bus, Service Container, Configuration)
- [x] Basic UI module with panel window
- [x] App Manager with SQLite database
- [x] Renderer process with panel UI
- [x] IPC communication between main and renderer
- [x] TypeScript configuration and tooling

### 🚧 In Progress
- [ ] WebEngine module (WebView integration)
- [ ] Session management (cookie/cache isolation)
- [ ] Activation engine (mouse hover, hotkeys)
- [ ] Notifications integration
- [ ] Auto-updater
- [ ] Theme system
- [ ] Animation system

### 📋 TODO
- [ ] Favicon fetching and caching
- [ ] Global hotkeys implementation
- [ ] Settings dialog
- [ ] Testing suite
- [ ] Build and packaging scripts
- [ ] Documentation updates

## Architecture

The new Electron implementation follows the same modular architecture as the C# version:

```
src/
├── main/           # Electron main process
│   ├── main.ts     # Application entry point
│   └── app-host.ts # Main orchestrator
├── renderer/       # Electron renderer process
│   ├── panel.html  # Panel UI
│   ├── renderer.ts # UI logic
│   └── preload.ts  # IPC bridge
├── shared/         # Shared types and utilities
│   └── types/      # TypeScript interfaces
└── modules/        # Feature modules
    ├── core/       # Core services
    ├── ui/         # UI management
    ├── webengine/  # Web content
    ├── appmanager/ # App CRUD
    ├── session/    # Session isolation
    ├── notifications/ # Native notifications
    ├── hotkeys/    # Global shortcuts
    └── updater/    # Auto-updates
```

## Technology Mapping

| C# Component | Electron Equivalent |
|--------------|-------------------|
| WPF + WebView2 | Electron BrowserWindow + webContents |
| Entity Framework + SQLite | better-sqlite3 |
| Microsoft.Extensions.Hosting | Custom service container |
| Win32 APIs | Electron native APIs |
| WPF Animations | CSS animations + Electron APIs |
| System.Text.Json | Native JSON |
| HtmlAgilityPack | cheerio |
| Octokit | @octokit/rest |

## Development

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Git

### Setup
```bash
# Install dependencies
npm install

# Build the application
npm run build

# Run in development mode
npm run dev

# Run with file watching
npm run dev:watch
```

### Building for Production
```bash
# Build for Windows
npm run dist:win

# Build for all platforms
npm run dist
```

## Key Differences from C# Version

### 1. **Event System**
- Replaced C# events with TypeScript EventEmitter pattern
- Maintained async event publishing
- Added type safety for event payloads

### 2. **Dependency Injection**
- Custom service container replaces Microsoft.Extensions.DependencyInjection
- Singleton, transient, and scoped lifetimes supported
- Factory pattern for complex service creation

### 3. **Configuration**
- electron-store replaces appsettings.json
- JSON schema validation
- Reactive configuration changes

### 4. **Database**
- better-sqlite3 replaces Entity Framework Core
- Direct SQL queries for better performance
- Maintained same database schema

### 5. **UI Framework**
- HTML/CSS/JS replaces WPF XAML
- Electron BrowserWindow replaces WPF Window
- CSS animations replace WPF Storyboards

## Migration Benefits

1. **Better Tooling**: Modern TypeScript/Node.js ecosystem
2. **Cross-Platform**: Runs on Windows, macOS, and Linux
3. **Web Technologies**: Easier UI development with HTML/CSS
4. **Package Management**: npm ecosystem vs NuGet
5. **Development Experience**: Hot reload, better debugging
6. **Deployment**: Simpler distribution with electron-builder

## Testing

```bash
# Run tests
npm test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch
```

## Contributing

1. Follow the existing modular architecture
2. Maintain TypeScript strict mode compliance
3. Add tests for new functionality
4. Update documentation for API changes
5. Use conventional commit messages

## Performance Targets

- **Memory Usage**: <100MB idle, <400MB active
- **Startup Time**: <2 seconds
- **Animation**: 60fps smooth animations
- **Database**: <100ms for CRUD operations

## Security

- Context isolation enabled
- Node integration disabled in renderer
- Secure IPC communication
- Input validation on all APIs
- CSP headers for web content

## Known Issues

1. WebView integration not yet implemented
2. Global hotkeys need native module
3. Activation engine requires mouse hooks
4. Theme system needs OS integration
5. Auto-updater needs code signing setup

## Next Steps

1. Complete WebEngine module with proper web view integration
2. Implement activation engine with mouse hover detection
3. Add global hotkeys support
4. Integrate native notifications
5. Complete theme system with OS detection
6. Add comprehensive testing suite
7. Set up CI/CD pipeline
8. Create installer packages

This migration provides a solid foundation for the SideView application with modern web technologies while maintaining all the core functionality of the original C# implementation.
