# Testing Guide for SideView Modal Layer Alignment Fixes

## Overview

This document provides comprehensive testing procedures to validate the modal layer alignment fixes implemented in the SideView application. The fixes address panel dimension consistency, BrowserView bounds calculation, and modal dialog z-index layering.

## Pre-Testing Setup

### **1. Start the Application**
```bash
npm run dev
```

### **2. Expected Startup Logs**
Look for these success indicators:
- ✅ `webpack 5.x.x compiled successfully`
- ✅ `Panel renderer initialized successfully`
- ✅ `Settings dialog IPC communication working: true`
- ✅ `SideView application started successfully!`

### **3. What NOT to See**
- ❌ No TypeScript compilation errors
- ❌ No "Correcting panel size" logs during normal operation
- ❌ No "Error setting panel position" messages

## Test Categories

## **Category 1: Panel Dimension Consistency**

### **Test 1.1: Initial Panel Dimensions**
**Objective**: Verify panel starts with correct hardcoded dimensions

**Steps:**
1. Start the application
2. Activate panel (move mouse to screen edge or use `Ctrl+Alt+Right`)
3. Observe panel appearance

**Expected Results:**
- Panel width: 280px
- Panel height: 600px
- No size correction logs in console
- Panel appears at consistent position

### **Test 1.2: Panel Show/Hide Cycles**
**Objective**: Ensure dimensions remain consistent across multiple interactions

**Steps:**
1. Activate panel (mouse hover or hotkey)
2. Deactivate panel (move mouse away)
3. Repeat 10 times
4. Monitor console logs

**Expected Results:**
- Panel maintains 280px × 600px dimensions throughout
- No "Correcting panel size" messages
- Smooth animations without position errors
- Consistent panel positioning

### **Test 1.3: Panel Pinning**
**Objective**: Verify dimensions remain stable when panel is pinned

**Steps:**
1. Activate panel
2. Click pin button (📌) in panel header
3. Perform various actions (tab switching, etc.)
4. Unpin panel
5. Hide and show panel again

**Expected Results:**
- Dimensions remain 280px × 600px when pinned
- No size changes during pinned state
- Consistent behavior after unpinning

## **Category 2: BrowserView Bounds Calculation**

### **Test 2.1: BrowserView Attachment**
**Objective**: Verify BrowserView uses correct bounds calculation

**Steps:**
1. Activate panel
2. Check console logs for BrowserView attachment message
3. Observe web content area

**Expected Results:**
- Log message: `Attached web app [name] to panel (bounds: {"x":0,"y":116,"width":280,"height":484})`
- Web content starts at y=116px (below 60px header + 56px tab bar)
- Web content area is 280px wide × 484px tall
- No overlap with UI components

### **Test 2.2: Component Alignment**
**Objective**: Ensure UI components align properly with BrowserView

**Steps:**
1. Activate panel with web content
2. Visually inspect alignment between:
   - Panel header (top 60px)
   - Tab bar (next 56px)
   - Web content area (remaining space)

**Expected Results:**
- Clear separation between UI components and web content
- No visual overlap or gaps
- Web content starts exactly below tab bar
- Consistent alignment across different websites

### **Test 2.3: Tab Switching**
**Objective**: Verify BrowserView bounds remain consistent during tab operations

**Steps:**
1. Activate panel
2. Switch between different tabs
3. Create new tab
4. Close tab
5. Monitor BrowserView bounds in logs

**Expected Results:**
- BrowserView bounds remain consistent: `{"x":0,"y":116,"width":280,"height":484}`
- No bounds recalculation during tab operations
- Smooth transitions between tabs

## **Category 3: Modal Dialog Z-Index Layering**

### **Test 3.1: Settings Dialog Modal**
**Objective**: Verify settings dialog appears above BrowserView content

**Steps:**
1. Activate panel with web content visible
2. Click settings button (⚙️) in panel header
3. Observe modal behavior
4. Close settings dialog

**Expected Results:**
- Settings dialog appears above all content
- BrowserView content is hidden/detached during modal
- Modal has proper backdrop blur effect
- Settings dialog is fully interactive
- BrowserView restores properly after modal closes

### **Test 3.2: Modal State Management**
**Objective**: Test modal state transitions and BrowserView management

**Steps:**
1. Activate panel
2. Open settings dialog
3. Check console logs for modal state messages
4. Close settings dialog
5. Verify BrowserView restoration

**Expected Results:**
- Log: `Modal state activated - BrowserView detached, window focused`
- Log: `Modal state deactivated - BrowserView restored`
- Panel window gains focus during modal
- BrowserView properly reattaches after modal

### **Test 3.3: Modal Error Recovery**
**Objective**: Test error handling in modal state management

**Steps:**
1. Open settings dialog
2. Force close dialog (if possible)
3. Try to open settings again
4. Monitor error recovery

**Expected Results:**
- No stuck modal states
- Proper error recovery mechanisms
- BrowserView state restored even after errors
- Settings dialog remains functional

## **Category 4: Edge Cases and Stress Testing**

### **Test 4.1: Rapid Panel Operations**
**Objective**: Test system stability under rapid interactions

**Steps:**
1. Rapidly show/hide panel 20 times
2. Quickly switch between tabs
3. Open/close settings dialog rapidly
4. Monitor system performance and logs

**Expected Results:**
- No memory leaks or performance degradation
- Consistent panel dimensions throughout
- No animation errors or position glitches
- Stable BrowserView attachment/detachment

### **Test 4.2: System Theme Changes**
**Objective**: Verify panel behavior during theme transitions

**Steps:**
1. Activate panel
2. Change system theme (light/dark)
3. Observe panel appearance and dimensions
4. Test modal dialogs after theme change

**Expected Results:**
- Panel dimensions remain unchanged during theme transitions
- Modal dialogs maintain proper z-index layering
- Visual appearance updates correctly
- No layout disruptions

### **Test 4.3: Multi-Monitor Setup**
**Objective**: Test panel behavior with multiple displays

**Steps:**
1. Connect second monitor (if available)
2. Move application between monitors
3. Test panel activation on different screens
4. Verify dimension consistency

**Expected Results:**
- Panel maintains 280px × 600px on all monitors
- Proper positioning calculations for different screens
- Consistent activation behavior across monitors

## **Category 5: Regression Testing**

### **Test 5.1: Previous Issues Verification**
**Objective**: Confirm original issues are resolved

**Steps:**
1. Test scenarios that previously caused size drift
2. Verify modal dialogs no longer interfere with BrowserView
3. Check component alignment issues are fixed

**Expected Results:**
- No size drift over time
- Modal dialogs properly layer above content
- All UI components properly aligned

### **Test 5.2: Settings Functionality**
**Objective**: Ensure settings dialog works without "toString" errors

**Steps:**
1. Open settings dialog multiple times
2. Modify various settings
3. Save and reload settings
4. Test with corrupted settings file (if possible)

**Expected Results:**
- No "Cannot read properties of undefined (reading 'toString')" errors
- Settings load and save properly
- Fallback to default settings works
- Settings dialog remains functional

### **Test 5.3: Tab Creation**
**Objective**: Verify tab creation works without failures

**Steps:**
1. Click "+" button to create new tab
2. Enter various URLs (valid and invalid)
3. Test tab creation with different inputs
4. Verify error handling

**Expected Results:**
- Tab creation succeeds with valid inputs
- Proper error messages for invalid inputs
- New tabs activate automatically
- No "Failed to create tab" errors

## Automated Testing Commands

### **Quick Verification Script**
```bash
# Start application and check for critical logs
npm run dev 2>&1 | grep -E "(Panel renderer initialized|Settings dialog IPC|Correcting panel size|Error setting panel position)"
```

### **Log Monitoring**
```bash
# Monitor for size correction issues (should be empty)
npm run dev 2>&1 | grep "Correcting panel size"

# Monitor for position errors (should be empty)
npm run dev 2>&1 | grep "Error setting panel position"
```

## Success Criteria

### **All Tests Pass When:**
- ✅ Panel maintains consistent 280px × 600px dimensions
- ✅ BrowserView bounds are always `{"x":0,"y":116,"width":280,"height":484}`
- ✅ Modal dialogs appear above BrowserView content
- ✅ No size correction or position error logs
- ✅ Settings dialog loads without "toString" errors
- ✅ Tab creation works reliably
- ✅ Component alignment is visually correct
- ✅ Performance remains stable under stress testing

### **Failure Indicators:**
- ❌ Panel size changes over time
- ❌ "Correcting panel size" logs appear
- ❌ "Error setting panel position" messages
- ❌ Modal dialogs appear behind BrowserView
- ❌ Component misalignment or overlap
- ❌ Settings or tab creation errors

## Reporting Issues

When reporting test failures, include:
1. **Test category and specific test number**
2. **Steps to reproduce**
3. **Expected vs actual results**
4. **Console logs (especially errors)**
5. **System information** (OS, screen resolution, etc.)
6. **Screenshots** (for visual issues)

## Conclusion

This comprehensive testing guide ensures all modal layer alignment fixes are properly validated. Regular execution of these tests will help maintain the stability and consistency of the SideView application's UI behavior.
