/**
 * App Manager Service - CRUD operations for web applications
 * Migrated from C# SideView.AppManager.Services.AppManagerService
 */

import Database from 'better-sqlite3';
// Temporarily disabled imports for testing
// import * as path from 'path';
// import { app } from 'electron';
import { WebAppModel, CreateAppRequest, UpdateAppRequest, SessionMode } from '@shared/types/app.types';
import { AppEventBus } from '@modules/core/event-bus';
import { AppManagerModuleStartedEvent, AppCreatedEvent, AppUpdatedEvent, AppDeletedEvent } from '@shared/types/events.types';

export class AppManagerService {
  private readonly eventBus: AppEventBus;
  private readonly logger: Console;
  private database?: Database.Database;
  private apps: WebAppModel[] = [];

  constructor(
    eventBus: AppEventBus,
    logger: Console = console
  ) {
    this.eventBus = eventBus;
    this.logger = logger;
  }

  async initialize(): Promise<void> {
    this.logger.info('Initializing AppManager Module');

    try {
      // Temporarily skip database initialization for testing
      // await this.initializeDatabase();
      await this.loadApps();

      this.logger.info('AppManager Module initialized successfully (without database)');
    } catch (error) {
      this.logger.error('Failed to initialize AppManager Module:', error);
      throw error;
    }
  }

  async start(): Promise<void> {
    this.logger.info('Starting AppManager Module');
    await this.eventBus.publish(new AppManagerModuleStartedEvent());
    this.logger.info('AppManager Module started successfully');
  }

  async stop(): Promise<void> {
    this.logger.info('Stopping AppManager Module');

    if (this.database) {
      this.database.close();
      delete (this as any).database;
    }

    this.logger.info('AppManager Module stopped successfully');
  }

  async getAllApps(): Promise<WebAppModel[]> {
    return [...this.apps];
  }

  async getAppById(id: string): Promise<WebAppModel | undefined> {
    return this.apps.find(app => app.id === id);
  }

  async createApp(request: CreateAppRequest): Promise<WebAppModel> {
    this.logger.info(`Creating new app: ${request.name} - ${request.url}`);
    
    try {
      // Validate request
      this.validateCreateRequest(request);
      
      // Create app model
      const app: WebAppModel = {
        id: this.generateId(),
        name: request.name.trim(),
        url: request.url.trim(),
        sessionMode: request.sessionMode,
        isActive: false,
        lastAccessed: new Date(),
        userDataPath: '',
        iconPath: '🌐', // Default icon
        order: this.getNextOrder(),
        isEnabled: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        badgeCount: 0,
        hasBadge: false
      };
      
      // Save to database (temporarily disabled)
      // await this.saveAppToDatabase(app);

      // Add to memory
      this.apps.push(app);
      
      // Publish event
      await this.eventBus.publish(new AppCreatedEvent(app));
      
      this.logger.info(`App created successfully: ${app.id}`);
      return app;
      
    } catch (error) {
      this.logger.error('Failed to create app:', error);
      throw error;
    }
  }

  async updateApp(request: UpdateAppRequest): Promise<WebAppModel> {
    this.logger.info(`Updating app: ${request.id}`);
    
    const app = this.apps.find(a => a.id === request.id);
    if (!app) {
      throw new Error(`App not found: ${request.id}`);
    }
    
    try {
      // Update properties
      if (request.name !== undefined) app.name = request.name.trim();
      if (request.url !== undefined) app.url = request.url.trim();
      if (request.sessionMode !== undefined) app.sessionMode = request.sessionMode;
      if (request.order !== undefined) app.order = request.order;
      if (request.isEnabled !== undefined) app.isEnabled = request.isEnabled;
      
      app.updatedAt = new Date();
      
      // Save to database (temporarily disabled)
      // await this.updateAppInDatabase(app);
      
      // Publish event
      await this.eventBus.publish(new AppUpdatedEvent(app));
      
      this.logger.info(`App updated successfully: ${app.id}`);
      return app;
      
    } catch (error) {
      this.logger.error('Failed to update app:', error);
      throw error;
    }
  }

  async deleteApp(appId: string): Promise<void> {
    this.logger.info(`Deleting app: ${appId}`);
    
    const app = this.apps.find(a => a.id === appId);
    if (!app) {
      throw new Error(`App not found: ${appId}`);
    }
    
    try {
      // Remove from database (temporarily disabled)
      // await this.deleteAppFromDatabase(appId);
      
      // Remove from memory
      this.apps = this.apps.filter(a => a.id !== appId);
      
      // Publish event
      await this.eventBus.publish(new AppDeletedEvent(appId, app.name));
      
      this.logger.info(`App deleted successfully: ${appId}`);
      
    } catch (error) {
      this.logger.error('Failed to delete app:', error);
      throw error;
    }
  }

  // Temporarily disabled for testing
  /*
  private async initializeDatabase(): Promise<void> {
    const userDataPath = app.getPath('userData');
    const dbPath = path.join(userDataPath, 'sideview.db');

    this.database = new Database(dbPath);

    // Create tables if they don't exist
    this.database.exec(`
      CREATE TABLE IF NOT EXISTS apps (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        url TEXT NOT NULL,
        sessionMode TEXT NOT NULL,
        isActive INTEGER NOT NULL DEFAULT 0,
        lastAccessed TEXT NOT NULL,
        userDataPath TEXT,
        iconPath TEXT,
        order_index INTEGER NOT NULL DEFAULT 0,
        isEnabled INTEGER NOT NULL DEFAULT 1,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL,
        badgeCount INTEGER DEFAULT 0,
        hasBadge INTEGER DEFAULT 0
      )
    `);

    this.logger.debug('Database initialized successfully');
  }
  */

  private async loadApps(): Promise<void> {
    // Temporarily load sample apps for testing (database disabled)
    const sampleApps: WebAppModel[] = [
      {
        id: 'sample-1',
        name: 'Google',
        url: 'https://www.google.com',
        sessionMode: 'shared' as SessionMode,
        isActive: false,
        lastAccessed: new Date(),
        userDataPath: '',
        iconPath: '🔍',
        order: 0,
        isEnabled: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        badgeCount: 0,
        hasBadge: false
      },
      {
        id: 'sample-2',
        name: 'GitHub',
        url: 'https://github.com',
        sessionMode: 'shared' as SessionMode,
        isActive: false,
        lastAccessed: new Date(),
        userDataPath: '',
        iconPath: '🐙',
        order: 1,
        isEnabled: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        badgeCount: 0,
        hasBadge: false
      }
    ];

    this.apps = sampleApps;
    this.logger.info(`Loaded ${this.apps.length} sample apps for testing`);
  }

  /*
  private async saveAppToDatabase(app: WebAppModel): Promise<void> {
    if (!this.database) {
      throw new Error('Database not initialized');
    }

    const stmt = this.database.prepare(`
      INSERT INTO apps (
        id, name, url, sessionMode, isActive, lastAccessed, userDataPath, iconPath,
        order_index, isEnabled, createdAt, updatedAt, badgeCount, hasBadge
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    stmt.run(
      app.id, app.name, app.url, app.sessionMode, app.isActive ? 1 : 0,
      app.lastAccessed.toISOString(), app.userDataPath, app.iconPath,
      app.order, app.isEnabled ? 1 : 0, app.createdAt.toISOString(),
      app.updatedAt.toISOString(), app.badgeCount || 0, app.hasBadge ? 1 : 0
    );
  }
  */

  /*
  private async updateAppInDatabase(app: WebAppModel): Promise<void> {
    if (!this.database) {
      throw new Error('Database not initialized');
    }

    const stmt = this.database.prepare(`
      UPDATE apps SET
        name = ?, url = ?, sessionMode = ?, isActive = ?, lastAccessed = ?,
        userDataPath = ?, iconPath = ?, order_index = ?, isEnabled = ?,
        updatedAt = ?, badgeCount = ?, hasBadge = ?
      WHERE id = ?
    `);

    stmt.run(
      app.name, app.url, app.sessionMode, app.isActive ? 1 : 0,
      app.lastAccessed.toISOString(), app.userDataPath, app.iconPath,
      app.order, app.isEnabled ? 1 : 0, app.updatedAt.toISOString(),
      app.badgeCount || 0, app.hasBadge ? 1 : 0, app.id
    );
  }
  */

  /*
  private async deleteAppFromDatabase(appId: string): Promise<void> {
    if (!this.database) {
      throw new Error('Database not initialized');
    }

    const stmt = this.database.prepare('DELETE FROM apps WHERE id = ?');
    stmt.run(appId);
  }
  */

  /*
  private mapRowToApp(row: any): WebAppModel {
    return {
      id: row.id,
      name: row.name,
      url: row.url,
      sessionMode: row.sessionMode as SessionMode,
      isActive: Boolean(row.isActive),
      lastAccessed: new Date(row.lastAccessed),
      userDataPath: row.userDataPath,
      iconPath: row.iconPath,
      order: row.order_index,
      isEnabled: Boolean(row.isEnabled),
      createdAt: new Date(row.createdAt),
      updatedAt: new Date(row.updatedAt),
      badgeCount: row.badgeCount,
      hasBadge: Boolean(row.hasBadge)
    };
  }
  */

  private validateCreateRequest(request: CreateAppRequest): void {
    if (!request.name || request.name.trim().length === 0) {
      throw new Error('App name is required');
    }
    
    if (!request.url || request.url.trim().length === 0) {
      throw new Error('App URL is required');
    }
    
    // Basic URL validation
    try {
      new URL(request.url);
    } catch {
      throw new Error('Invalid URL format');
    }
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  private getNextOrder(): number {
    return this.apps.length > 0 ? Math.max(...this.apps.map(a => a.order)) + 1 : 0;
  }
}
