/**
 * Configuration Service
 * Migrated from C# SideView.Core.Configuration.ConfigurationService
 */

import { EventEmitter } from 'events';
import Store from 'electron-store';
import { AppSettings, PanelPosition, Theme, NotificationPosition, UpdateChannel } from '@shared/types/app.types';

export interface ConfigurationChangedEventArgs {
  oldSettings: AppSettings;
  newSettings: AppSettings;
  changedKeys: string[];
}

export interface IConfigurationService {
  readonly settings: AppSettings;
  getSettings(): AppSettings;
  updateSettings(updates: Partial<AppSettings>): Promise<void>;
  resetToDefaults(): Promise<void>;
  on(event: 'configurationChanged', listener: (args: ConfigurationChangedEventArgs) => void): this;
  off(event: 'configurationChanged', listener: (args: ConfigurationChangedEventArgs) => void): this;
}

export class ConfigurationService extends EventEmitter implements IConfigurationService {
  private readonly store: Store<AppSettings>;
  private readonly logger: Console;
  private _settings: AppSettings;

  constructor(logger: Console = console) {
    super();
    this.logger = logger;

    try {
      // Initialize electron-store with schema validation and error handling
      this.store = new Store<AppSettings>({
        name: 'sideview-settings',
        defaults: this.getDefaultSettings(),
        schema: this.getSettingsSchema(),
        clearInvalidConfig: true // Clear invalid config instead of throwing
      });

      // Load current settings with fallback
      try {
        this._settings = this.store.store;
        this.logger.info('Configuration loaded successfully');
      } catch (loadError) {
        this.logger.warn('Failed to load settings, using defaults:', loadError);
        this._settings = this.getDefaultSettings();
        this.store.store = this._settings;
      }

      // Validate loaded settings
      this.validateSettings(this._settings);

      this.logger.debug('Configuration service initialized successfully');

    } catch (error) {
      this.logger.error('Failed to initialize configuration service:', error);
      // Fallback to defaults if everything fails
      this._settings = this.getDefaultSettings();
      throw new Error(`Configuration service initialization failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  get settings(): AppSettings {
    return { ...this._settings };
  }

  getSettings(): AppSettings {
    return this.settings;
  }

  async updateSettings(updates: Partial<AppSettings>): Promise<void> {
    const oldSettings = { ...this._settings };

    try {
      const newSettings = this.mergeSettings(this._settings, updates);
      const changedKeys = this.getChangedKeys(oldSettings, newSettings);

      if (changedKeys.length === 0) {
        this.logger.debug('No configuration changes detected');
        return;
      }

      // Validate new settings before applying
      this.validateSettings(newSettings);

      // Backup current settings in case of failure
      const backupSettings = { ...this._settings };

      try {
        // Update store
        this.store.store = newSettings;
        this._settings = newSettings;

        this.logger.info(`Configuration updated. Changed keys: ${changedKeys.join(', ')}`);

        // Emit change event
        this.emit('configurationChanged', {
          oldSettings,
          newSettings,
          changedKeys
        } as ConfigurationChangedEventArgs);

      } catch (storeError) {
        // Restore backup if store update fails
        this._settings = backupSettings;
        this.logger.error('Failed to persist settings, restored backup:', storeError);
        throw new Error(`Failed to save settings: ${storeError instanceof Error ? storeError.message : String(storeError)}`);
      }

    } catch (error) {
      this.logger.error('Failed to update configuration:', error);
      throw error;
    }
  }

  async resetToDefaults(): Promise<void> {
    const oldSettings = { ...this._settings };
    const defaultSettings = this.getDefaultSettings();

    this.store.clear();
    this.store.store = defaultSettings;
    this._settings = defaultSettings;

    this.logger.info('Configuration reset to defaults');

    this.emit('configurationChanged', {
      oldSettings,
      newSettings: defaultSettings,
      changedKeys: Object.keys(defaultSettings)
    } as ConfigurationChangedEventArgs);
  }



  private getDefaultSettings(): AppSettings {
    return {
      ui: {
        panelWidth: 280, // Consistent with user preference range 250-300px
        panelHeight: 600,
        panelPosition: PanelPosition.Left,
        theme: Theme.System,
        animationDuration: 250,
        autoHide: true,
        activationDelay: 100,
        edgeActivation: {
          left: {
            enabled: true,
            position: 'middle',
            size: 50, // 50% of screen edge
            width: 4,
            offset: 0
          },
          right: {
            enabled: false,
            position: 'middle',
            size: 50,
            width: 4,
            offset: 0
          },
          top: {
            enabled: false,
            position: 'middle',
            size: 50,
            width: 4,
            offset: 0
          },
          bottom: {
            enabled: false,
            position: 'middle',
            size: 50,
            width: 4,
            offset: 0
          }
        }
      },
      webEngine: {
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) SideView/1.0',
        enableDevTools: false,
        enableJavaScript: true,
        enableImages: true,
        enablePlugins: false,
        defaultZoomLevel: 1.0
      },
      notifications: {
        enabled: true,
        showBadges: true,
        soundEnabled: true,
        position: NotificationPosition.TopRight
      },
      hotkeys: {
        togglePanel: 'CommandOrControl+Alt+Right',
        newTab: 'CommandOrControl+T',
        refresh: 'F5',
        enabled: true
      },
      updater: {
        enabled: true,
        checkInterval: 3600000, // 1 hour
        autoDownload: true,
        autoInstall: false,
        channel: UpdateChannel.Stable
      }
    };
  }

  private getSettingsSchema(): any {
    return {
      ui: {
        type: 'object',
        properties: {
          panelWidth: { type: 'number', minimum: 250, maximum: 800 },
          panelHeight: { type: 'number', minimum: 400, maximum: 1200 },
          panelPosition: { type: 'string', enum: Object.values(PanelPosition) },
          theme: { type: 'string', enum: Object.values(Theme) },
          animationDuration: { type: 'number', minimum: 0, maximum: 1000 },
          autoHide: { type: 'boolean' },
          activationDelay: { type: 'number', minimum: 0, maximum: 1000 },
          edgeActivation: {
            type: 'object',
            properties: {
              left: {
                type: 'object',
                properties: {
                  enabled: { type: 'boolean' },
                  position: { type: 'string', enum: ['top', 'middle', 'bottom', 'full'] },
                  size: { type: 'number', minimum: 10, maximum: 100 },
                  width: { type: 'number', minimum: 1, maximum: 20 },
                  offset: { type: 'number', minimum: 0, maximum: 500 }
                }
              },
              right: {
                type: 'object',
                properties: {
                  enabled: { type: 'boolean' },
                  position: { type: 'string', enum: ['top', 'middle', 'bottom', 'full'] },
                  size: { type: 'number', minimum: 10, maximum: 100 },
                  width: { type: 'number', minimum: 1, maximum: 20 },
                  offset: { type: 'number', minimum: 0, maximum: 500 }
                }
              },
              top: {
                type: 'object',
                properties: {
                  enabled: { type: 'boolean' },
                  position: { type: 'string', enum: ['top', 'middle', 'bottom', 'full'] },
                  size: { type: 'number', minimum: 10, maximum: 100 },
                  width: { type: 'number', minimum: 1, maximum: 20 },
                  offset: { type: 'number', minimum: 0, maximum: 500 }
                }
              },
              bottom: {
                type: 'object',
                properties: {
                  enabled: { type: 'boolean' },
                  position: { type: 'string', enum: ['top', 'middle', 'bottom', 'full'] },
                  size: { type: 'number', minimum: 10, maximum: 100 },
                  width: { type: 'number', minimum: 1, maximum: 20 },
                  offset: { type: 'number', minimum: 0, maximum: 500 }
                }
              }
            }
          }
        }
      },
      webEngine: {
        type: 'object',
        properties: {
          userAgent: { type: 'string' },
          enableDevTools: { type: 'boolean' },
          enableJavaScript: { type: 'boolean' },
          enableImages: { type: 'boolean' },
          enablePlugins: { type: 'boolean' },
          defaultZoomLevel: { type: 'number', minimum: 0.5, maximum: 3.0 }
        }
      },
      notifications: {
        type: 'object',
        properties: {
          enabled: { type: 'boolean' },
          showBadges: { type: 'boolean' },
          soundEnabled: { type: 'boolean' },
          position: { type: 'string', enum: Object.values(NotificationPosition) }
        }
      },
      hotkeys: {
        type: 'object',
        properties: {
          togglePanel: { type: 'string' },
          newTab: { type: 'string' },
          refresh: { type: 'string' },
          enabled: { type: 'boolean' }
        }
      },
      updater: {
        type: 'object',
        properties: {
          enabled: { type: 'boolean' },
          checkInterval: { type: 'number', minimum: 60000 },
          autoDownload: { type: 'boolean' },
          autoInstall: { type: 'boolean' },
          channel: { type: 'string', enum: Object.values(UpdateChannel) }
        }
      }
    };
  }

  private mergeSettings(current: AppSettings, updates: Partial<AppSettings>): AppSettings {
    return {
      ui: { ...current.ui, ...updates.ui },
      webEngine: { ...current.webEngine, ...updates.webEngine },
      notifications: { ...current.notifications, ...updates.notifications },
      hotkeys: { ...current.hotkeys, ...updates.hotkeys },
      updater: { ...current.updater, ...updates.updater }
    };
  }

  private getChangedKeys(oldSettings: AppSettings, newSettings: AppSettings): string[] {
    const changes: string[] = [];
    
    for (const [section, values] of Object.entries(newSettings)) {
      for (const [key, value] of Object.entries(values)) {
        const oldValue = (oldSettings as any)[section][key];
        if (JSON.stringify(oldValue) !== JSON.stringify(value)) {
          changes.push(`${section}.${key}`);
        }
      }
    }
    
    return changes;
  }

  private validateSettings(settings: AppSettings): void {
    // Basic validation - could be enhanced with a proper schema validator
    if (settings.ui.panelWidth < 250 || settings.ui.panelWidth > 800) {
      throw new Error('Panel width must be between 250 and 800 pixels');
    }

    if (settings.ui.panelHeight < 400 || settings.ui.panelHeight > 1200) {
      throw new Error('Panel height must be between 400 and 1200 pixels');
    }

    if (settings.ui.animationDuration < 0 || settings.ui.animationDuration > 1000) {
      throw new Error('Animation duration must be between 0 and 1000 milliseconds');
    }

    if (settings.webEngine.defaultZoomLevel < 0.5 || settings.webEngine.defaultZoomLevel > 3.0) {
      throw new Error('Zoom level must be between 0.5 and 3.0');
    }
  }
}
