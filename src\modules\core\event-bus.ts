/**
 * Event Bus implementation
 * Migrated from C# SideView.Core.Events.AppEventBus
 */

import { IAppEvent, IAppEventBus, EventHandler } from '@shared/types/events.types';

export class AppEventBus implements IAppEventBus {
  private readonly handlers = new Map<string, Set<EventHandler<any>>>();
  private readonly logger: Console;

  constructor(logger: Console = console) {
    this.logger = logger;
  }

  /**
   * Publishes an event to all registered handlers
   */
  async publish<T extends IAppEvent>(event: T): Promise<void> {
    const eventType = event.eventType;
    const handlers = this.handlers.get(eventType);

    if (!handlers || handlers.size === 0) {
      this.logger.debug(`No handlers registered for event type: ${eventType}`);
      return;
    }

    this.logger.debug(`Publishing event ${eventType} to ${handlers.size} handlers`);

    // Execute all handlers concurrently
    const promises: Promise<void>[] = [];
    
    for (const handler of handlers) {
      try {
        const result = handler(event);
        if (result instanceof Promise) {
          promises.push(result);
        }
      } catch (error) {
        this.logger.error(`Error in event handler for ${eventType}:`, error);
      }
    }

    // Wait for all async handlers to complete
    if (promises.length > 0) {
      try {
        await Promise.all(promises);
      } catch (error) {
        this.logger.error(`Error in async event handlers for ${eventType}:`, error);
      }
    }
  }

  /**
   * Subscribes a handler to an event type
   */
  subscribe<T extends IAppEvent>(eventType: string, handler: EventHandler<T>): void {
    if (!this.handlers.has(eventType)) {
      this.handlers.set(eventType, new Set());
    }

    const handlers = this.handlers.get(eventType)!;
    handlers.add(handler);

    this.logger.debug(`Subscribed handler to event type: ${eventType} (${handlers.size} total handlers)`);
  }

  /**
   * Unsubscribes a handler from an event type
   */
  unsubscribe<T extends IAppEvent>(eventType: string, handler: EventHandler<T>): void {
    const handlers = this.handlers.get(eventType);
    if (!handlers) {
      return;
    }

    const removed = handlers.delete(handler);
    if (removed) {
      this.logger.debug(`Unsubscribed handler from event type: ${eventType} (${handlers.size} remaining handlers)`);
      
      // Clean up empty handler sets
      if (handlers.size === 0) {
        this.handlers.delete(eventType);
      }
    }
  }

  /**
   * Clears all event handlers
   */
  clear(): void {
    const totalHandlers = Array.from(this.handlers.values())
      .reduce((sum, handlers) => sum + handlers.size, 0);
    
    this.handlers.clear();
    this.logger.debug(`Cleared all event handlers (${totalHandlers} total)`);
  }

  /**
   * Gets the number of handlers for a specific event type
   */
  getHandlerCount(eventType: string): number {
    const handlers = this.handlers.get(eventType);
    return handlers ? handlers.size : 0;
  }

  /**
   * Gets all registered event types
   */
  getEventTypes(): string[] {
    return Array.from(this.handlers.keys());
  }

  /**
   * Gets statistics about the event bus
   */
  getStats(): { eventTypes: number; totalHandlers: number; eventTypeStats: Record<string, number> } {
    const eventTypeStats: Record<string, number> = {};
    let totalHandlers = 0;

    for (const [eventType, handlers] of this.handlers) {
      const count = handlers.size;
      eventTypeStats[eventType] = count;
      totalHandlers += count;
    }

    return {
      eventTypes: this.handlers.size,
      totalHandlers,
      eventTypeStats
    };
  }
}
