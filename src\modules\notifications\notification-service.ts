/**
 * Notification Service - Native Windows notifications
 * Migrated from C# SideView.Notifications.Services.NotificationService
 */

import { AppEventBus } from '@modules/core/event-bus';

export class NotificationService {
  private readonly _eventBus: AppEventBus;
  private readonly logger: Console;

  constructor(
    eventBus: AppEventBus,
    logger: Console = console
  ) {
    this._eventBus = eventBus;
    this.logger = logger;
    void this._eventBus; // Mark as used for stub implementation
  }

  async initialize(): Promise<void> {
    this.logger.info('Initializing Notifications Module');
    // TODO: Initialize notification system
    this.logger.info('Notifications Module initialized successfully');
  }

  async start(): Promise<void> {
    this.logger.info('Starting Notifications Module');
    this.logger.info('Notifications Module started successfully');
  }

  async stop(): Promise<void> {
    this.logger.info('Stopping Notifications Module');
    this.logger.info('Notifications Module stopped successfully');
  }
}
