/**
 * Session Manager Service - Cookie and cache isolation
 * Migrated from C# SideView.SessionMgr.Services.SessionManagerService
 */

import { session as electronSession, app } from 'electron';
import * as path from 'path';
import { WebAppModel, SessionMode } from '@shared/types/app.types';
import { AppEventBus } from '@modules/core/event-bus';

export class SessionManagerService {
  private readonly _eventBus: AppEventBus;
  private readonly logger: Console;
  private readonly sessions = new Map<string, Electron.Session>();
  private sharedSession?: Electron.Session;
  private isInitialized = false;

  constructor(
    eventBus: AppEventBus,
    logger: Console = console
  ) {
    this._eventBus = eventBus;
    this.logger = logger;
    void this._eventBus; // Mark as used for stub implementation
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    this.logger.info('Initializing SessionManager Module');

    try {
      // Create shared session
      await this.createSharedSession();

      this.isInitialized = true;
      this.logger.info('SessionManager Module initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize SessionManager Module:', error);
      throw error;
    }
  }

  async start(): Promise<void> {
    this.logger.info('Starting SessionManager Module');
    this.logger.info('SessionManager Module started successfully');
  }

  async stop(): Promise<void> {
    this.logger.info('Stopping SessionManager Module');

    // Clean up sessions
    for (const [appId, session] of this.sessions) {
      try {
        await this.cleanupSession(session);
      } catch (error) {
        this.logger.error(`Error cleaning up session for app ${appId}:`, error);
      }
    }

    this.sessions.clear();
    delete (this as any).sharedSession;
    this.isInitialized = false;

    this.logger.info('SessionManager Module stopped successfully');
  }

  async getSessionForApp(app: WebAppModel): Promise<Electron.Session> {
    if (!this.isInitialized) {
      throw new Error('SessionManager not initialized');
    }

    if (app.sessionMode === SessionMode.Shared) {
      return this.getSharedSession();
    } else {
      return this.getIsolatedSession(app);
    }
  }

  async createIsolatedSession(app: WebAppModel): Promise<Electron.Session> {
    const sessionId = `isolated-${app.id}`;

    if (this.sessions.has(sessionId)) {
      return this.sessions.get(sessionId)!;
    }

    this.logger.info(`Creating isolated session for app ${app.id}: ${app.name}`);

    try {
      // Create session with unique partition
      const session = electronSession.fromPartition(`persist:${sessionId}`, {
        cache: true
      });

      // Configure session
      await this.configureSession(session, app);

      // Store session
      this.sessions.set(sessionId, session);

      this.logger.info(`Isolated session created for app ${app.id}`);
      return session;

    } catch (error) {
      this.logger.error(`Failed to create isolated session for app ${app.id}:`, error);
      throw error;
    }
  }

  async removeSessionForApp(appId: string): Promise<void> {
    const sessionId = `isolated-${appId}`;
    const session = this.sessions.get(sessionId);

    if (!session) {
      this.logger.debug(`No session found for app ${appId}`);
      return;
    }

    this.logger.info(`Removing session for app ${appId}`);

    try {
      await this.cleanupSession(session);
      this.sessions.delete(sessionId);

      this.logger.info(`Session removed for app ${appId}`);
    } catch (error) {
      this.logger.error(`Error removing session for app ${appId}:`, error);
      throw error;
    }
  }

  async clearSessionData(appId: string): Promise<void> {
    const sessionId = `isolated-${appId}`;
    const session = this.sessions.get(sessionId);

    if (!session) {
      this.logger.warn(`No session found for app ${appId}`);
      return;
    }

    this.logger.info(`Clearing session data for app ${appId}`);

    try {
      // Clear cache
      await session.clearCache();

      // Clear storage data
      await session.clearStorageData({
        storages: ['cookies', 'localstorage', 'indexdb', 'websql']
      });

      this.logger.info(`Session data cleared for app ${appId}`);
    } catch (error) {
      this.logger.error(`Error clearing session data for app ${appId}:`, error);
      throw error;
    }
  }

  private getSharedSession(): Electron.Session {
    if (!this.sharedSession) {
      throw new Error('Shared session not initialized');
    }
    return this.sharedSession;
  }

  private getIsolatedSession(app: WebAppModel): Electron.Session {
    const sessionId = `isolated-${app.id}`;
    let session = this.sessions.get(sessionId);

    if (!session) {
      // Create new isolated session
      session = electronSession.fromPartition(`persist:${sessionId}`, {
        cache: true
      });

      this.configureSession(session, app);
      this.sessions.set(sessionId, session);

      this.logger.debug(`Created new isolated session for app ${app.id}`);
    }

    return session;
  }

  private async createSharedSession(): Promise<void> {
    this.logger.debug('Creating shared session');

    try {
      // Use default session for shared mode
      this.sharedSession = electronSession.defaultSession;

      // Configure shared session
      await this.configureSharedSession();

      this.logger.debug('Shared session created successfully');
    } catch (error) {
      this.logger.error('Failed to create shared session:', error);
      throw error;
    }
  }

  private async configureSession(session: Electron.Session, webApp: WebAppModel): Promise<void> {
    try {
      // Set user data path for isolated sessions
      if (webApp.sessionMode === SessionMode.Isolated) {
        const userDataPath = path.join(app.getPath('userData'), 'sessions', webApp.id);
        webApp.userDataPath = userDataPath;
      }

      // Configure security
      session.setPermissionRequestHandler((_webContents, permission, callback) => {
        // Allow notifications for web apps
        if (permission === 'notifications') {
          callback(true);
          return;
        }

        // Allow media access for web apps that need it
        if (permission === 'media') {
          callback(true);
          return;
        }

        // Deny other permissions by default
        callback(false);
      });

      // Configure certificate error handling
      session.setCertificateVerifyProc((request, callback) => {
        // Allow localhost for development
        if (request.hostname === 'localhost' || request.hostname === '127.0.0.1') {
          callback(0); // Accept
          return;
        }

        // Use default verification for other hosts
        callback(-2); // Use default verification
      });

      // Set download behavior
      session.on('will-download', (event, item, _webContents) => {
        // Prevent downloads by default for security
        event.preventDefault();
        this.logger.warn(`Download blocked for app ${webApp.id}: ${item.getFilename()}`);
      });

      this.logger.debug(`Session configured for app ${webApp.id}`);

    } catch (error) {
      this.logger.error(`Failed to configure session for app ${webApp.id}:`, error);
      throw error;
    }
  }

  private async configureSharedSession(): Promise<void> {
    if (!this.sharedSession) {
      throw new Error('Shared session not available');
    }

    try {
      // Configure shared session permissions
      this.sharedSession.setPermissionRequestHandler((_webContents, permission, callback) => {
        // Allow notifications
        if (permission === 'notifications') {
          callback(true);
          return;
        }

        // Allow media access
        if (permission === 'media') {
          callback(true);
          return;
        }

        // Deny other permissions
        callback(false);
      });

      // Set download behavior for shared session
      this.sharedSession.on('will-download', (event, item, _webContents) => {
        event.preventDefault();
        this.logger.warn(`Download blocked in shared session: ${item.getFilename()}`);
      });

      this.logger.debug('Shared session configured');

    } catch (error) {
      this.logger.error('Failed to configure shared session:', error);
      throw error;
    }
  }

  private async cleanupSession(session: Electron.Session): Promise<void> {
    try {
      // Clear cache and storage data
      await session.clearCache();
      await session.clearStorageData();

      this.logger.debug('Session cleaned up successfully');
    } catch (error) {
      this.logger.error('Error cleaning up session:', error);
      // Don't throw - this is cleanup
    }
  }
}
